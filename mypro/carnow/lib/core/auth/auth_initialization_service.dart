// =============================================================================
// CARNOW AUTH INITIALIZATION SERVICE - Forever Plan Architecture
// =============================================================================
//
// This service manages the complete authentication system initialization process
// with proper error handling, state management, and configuration validation.
//
// Architecture: Flutter (UI Only) → Go API → Supabase (Data Only)
//
// Key Features:
// - Synchronous provider setup to eliminate race conditions
// - Comprehensive error handling and fallback mechanisms
// - Configuration validation for all required auth settings
// - Circuit breaker pattern for resilient initialization
// - Graceful degradation when initialization fails
// - Production-ready logging and monitoring
//
// Author: CarNow Development Team
// =============================================================================

import 'dart:async';
import 'dart:developer' as developer;

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'auth_models.dart';
import 'auth_interfaces.dart';
import 'enhanced_secure_token_storage.dart';
import 'google_auth_service.dart';
import '../config/backend_config.dart';

part 'auth_initialization_service.g.dart';
part 'auth_initialization_service.freezed.dart';

// =============================================================================
// AUTH CONFIGURATION MODEL
// =============================================================================

/// Configuration model for authentication initialization
@freezed
abstract class AuthConfiguration with _$AuthConfiguration {
  const factory AuthConfiguration({
    /// Whether Google OAuth is enabled
    @Default(true) bool googleAuthEnabled,

    /// Whether email authentication is enabled
    @Default(true) bool emailAuthEnabled,

    /// API base URL for backend communication
    required String apiBaseUrl,

    /// Token refresh interval
    @Default(Duration(minutes: 45)) Duration tokenRefreshInterval,

    /// Maximum time to wait for initialization
    @Default(Duration(seconds: 15)) Duration initializationTimeout,

    /// Maximum number of retry attempts
    @Default(3) int maxRetryAttempts,

    /// Whether offline mode is enabled
    @Default(true) bool enableOfflineMode,

    /// Whether to validate stored tokens on startup
    @Default(true) bool validateStoredTokens,

    /// Whether to use secure storage for tokens
    @Default(true) bool useSecureStorage,
  }) = _AuthConfiguration;

  const AuthConfiguration._();

  /// Validate configuration parameters
  bool get isValid {
    return apiBaseUrl.isNotEmpty &&
        initializationTimeout.inSeconds > 0 &&
        maxRetryAttempts > 0 &&
        tokenRefreshInterval.inMinutes > 0;
  }

  /// Get configuration errors
  List<String> get validationErrors {
    final errors = <String>[];

    if (apiBaseUrl.isEmpty) {
      errors.add('API base URL is required');
    }

    if (initializationTimeout.inSeconds <= 0) {
      errors.add('Initialization timeout must be positive');
    }

    if (maxRetryAttempts <= 0) {
      errors.add('Max retry attempts must be positive');
    }

    if (tokenRefreshInterval.inMinutes <= 0) {
      errors.add('Token refresh interval must be positive');
    }

    if (!googleAuthEnabled && !emailAuthEnabled) {
      errors.add('At least one authentication method must be enabled');
    }

    return errors;
  }
}

// =============================================================================
// AUTH INITIALIZATION RESULT MODEL
// =============================================================================

/// Result of authentication initialization process
@freezed
class AuthInitializationResult with _$AuthInitializationResult {
  /// Successful initialization
  const factory AuthInitializationResult.success({
    /// Initial authentication state
    required AuthState initialState,

    /// Time taken for initialization
    required Duration initializationTime,

    /// Configuration used for initialization
    required AuthConfiguration configuration,

    /// Additional metadata
    @Default({}) Map<String, dynamic> metadata,
  }) = AuthInitializationSuccess;

  /// Failed initialization
  const factory AuthInitializationResult.failure({
    /// Error message
    required String error,

    /// Error type for categorization
    required AuthErrorType errorType,

    /// Whether initialization can be retried
    required bool canRetry,

    /// Fallback state to use
    AuthState? fallbackState,

    /// Additional error details
    @Default({}) Map<String, dynamic> details,
  }) = AuthInitializationFailure;

  /// Initialization timeout
  const factory AuthInitializationResult.timeout({
    /// Duration that was waited
    required Duration timeoutDuration,

    /// Fallback state to use
    required AuthState fallbackState,

    /// Whether retry is recommended
    @Default(true) bool shouldRetry,
  }) = AuthInitializationTimeout;
}

// =============================================================================
// AUTH INITIALIZATION SERVICE
// =============================================================================

/// Service responsible for robust authentication system initialization
class AuthInitializationService {
  // ---------------------------------------------------------------------------
  // Dependencies and Configuration
  // ---------------------------------------------------------------------------

  final Ref _ref;
  final AuthConfiguration _configuration;

  late final ITokenStorage _tokenStorage;
  late final Dio _internalDio; // Internal Dio instance to avoid circular dependency
  late final GoogleAuthService _googleAuthService;

  // Circuit breaker state
  int _failureCount = 0;
  DateTime? _lastFailureTime;
  bool _circuitOpen = false;

  // Initialization state
  bool _isInitializing = false;
  bool _hasInitialized = false;
  AuthInitializationResult? _lastResult;

  // ---------------------------------------------------------------------------
  // Constructor and Setup
  // ---------------------------------------------------------------------------

  AuthInitializationService({
    required Ref ref,
    AuthConfiguration? configuration,
  }) : _ref = ref,
       _configuration = configuration ?? _createDefaultConfiguration() {
    _initializeDependencies();
  }

  /// Create default configuration
  static AuthConfiguration _createDefaultConfiguration() {
    return const AuthConfiguration(
      apiBaseUrl: 'https://backend-go-8klm.onrender.com',
      googleAuthEnabled: true,
      emailAuthEnabled: true,
      initializationTimeout: Duration(seconds: 60), // Increased from 15 to 60 seconds
      maxRetryAttempts: 3,
      enableOfflineMode: true,
      validateStoredTokens: true,
      useSecureStorage: true,
    );
  }

  /// Initialize service dependencies
  void _initializeDependencies() {
    try {
      _tokenStorage = _ref.read(enhancedSecureTokenStorageProvider);
      _internalDio = _createInternalDio(); // Create internal Dio instance
      _googleAuthService = _ref.read(googleAuthServiceProvider);

      developer.log(
        'AuthInitializationService dependencies initialized',
        name: 'AuthInitializationService',
      );
    } catch (error, stackTrace) {
      developer.log(
        'Failed to initialize dependencies: $error',
        name: 'AuthInitializationService',
        error: error,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Create internal Dio instance for API calls
  /// This avoids circular dependency with SimpleApiClient
  Dio _createInternalDio() {
    final dio = Dio();
    
    // Configure base options
    dio.options = BaseOptions(
      baseUrl: BackendConfig.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );
    
    return dio;
  }

  // ---------------------------------------------------------------------------
  // Main Initialization Methods
  // ---------------------------------------------------------------------------

  /// Initialize authentication system with comprehensive error handling
  Future<AuthInitializationResult> initialize() async {
    if (_isInitializing) {
      developer.log(
        'Initialization already in progress',
        name: 'AuthInitializationService',
      );
      return _lastResult ??
          const AuthInitializationResult.failure(
            error: 'Initialization already in progress',
            errorType: AuthErrorType.unknown,
            canRetry: false,
          );
    }

    if (_hasInitialized && _lastResult != null) {
      developer.log(
        'Already initialized, returning cached result',
        name: 'AuthInitializationService',
      );
      return _lastResult!;
    }

    final stopwatch = Stopwatch()..start();
    _isInitializing = true;

    try {
      developer.log(
        'Starting authentication system initialization',
        name: 'AuthInitializationService',
      );

      // Validate configuration first
      final configValidation = await validateConfiguration();
      if (!configValidation.isValid) {
        return _handleInitializationFailure(
          'Configuration validation failed: ${configValidation.errors.join(', ')}',
          AuthErrorType.unknown,
          canRetry: false,
        );
      }

      // Check circuit breaker
      if (_isCircuitOpen()) {
        return _handleCircuitBreakerOpen();
      }

      // Perform initialization with timeout
      final result = await _performInitializationWithTimeout();

      stopwatch.stop();

      // Update circuit breaker state
      _updateCircuitBreakerOnSuccess();

      // Cache successful result
      _lastResult = result;
      _hasInitialized = true;

      developer.log(
        'Authentication initialization completed in ${stopwatch.elapsedMilliseconds}ms',
        name: 'AuthInitializationService',
      );

      return result;
    } catch (error, stackTrace) {
      stopwatch.stop();

      developer.log(
        'Authentication initialization failed: $error',
        name: 'AuthInitializationService',
        error: error,
        stackTrace: stackTrace,
      );

      // Update circuit breaker state
      _updateCircuitBreakerOnFailure();

      // Determine error type and create failure result
      final errorType = _categorizeError(error);
      final result = _handleInitializationFailure(
        error.toString(),
        errorType,
        canRetry: _canRetryError(errorType),
      );

      _lastResult = result;
      return result;
    } finally {
      _isInitializing = false;
    }
  }

  /// Perform initialization with timeout protection
  Future<AuthInitializationResult> _performInitializationWithTimeout() async {
    return await Future.any([
      _performActualInitialization(),
      _createTimeoutFuture(),
    ]);
  }

  /// Create timeout future
  Future<AuthInitializationResult> _createTimeoutFuture() async {
    await Future.delayed(_configuration.initializationTimeout);

    developer.log(
      'Initialization timeout after ${_configuration.initializationTimeout.inSeconds}s',
      name: 'AuthInitializationService',
    );

    return AuthInitializationResult.timeout(
      timeoutDuration: _configuration.initializationTimeout,
      fallbackState: const AuthState.unauthenticated(
        reason: 'انتهت مهلة تهيئة نظام المصادقة',
      ),
      shouldRetry: true,
    );
  }

  /// Perform the actual initialization logic
  Future<AuthInitializationResult> _performActualInitialization() async {
    final stopwatch = Stopwatch()..start();

    try {
      // Step 1: Initialize token storage
      await _initializeTokenStorage();

      // Step 2: Check for existing authentication state
      final existingState = await _checkExistingAuthState();

      // Step 3: Validate stored tokens if present
      if (existingState is AuthStateAuthenticated &&
          _configuration.validateStoredTokens) {
        final validatedState = await _validateStoredTokens(existingState);
        if (validatedState != null) {
          stopwatch.stop();
          return AuthInitializationResult.success(
            initialState: validatedState,
            initializationTime: stopwatch.elapsed,
            configuration: _configuration,
            metadata: {
              'method': 'token_validation',
              'user_id': validatedState.when(
                authenticated:
                    (user, token, refreshToken, tokenExpiry, sessionStart) =>
                        user.id,
                initial: () => 'unknown',
                loading: (message, operation) => 'unknown',
                unauthenticated: (reason) => 'unknown',
                error:
                    (
                      message,
                      errorCode,
                      errorType,
                      isRecoverable,
                      originalException,
                    ) => 'unknown',
                emailVerificationPending: (email, sentAt) => 'unknown',
                sessionExpired: (expiredAt, autoRefreshAttempted) => 'unknown',
              ),
            },
          );
        }
      }

      // Step 4: Initialize auth providers
      await _initializeAuthProviders();

      // Step 5: Set up monitoring and cleanup
      _setupMonitoringAndCleanup();

      stopwatch.stop();

      // Return successful initialization with appropriate state
      final finalState =
          existingState ??
          const AuthState.unauthenticated(
            reason: 'لم يتم العثور على جلسة مصادقة سابقة',
          );

      return AuthInitializationResult.success(
        initialState: finalState,
        initializationTime: stopwatch.elapsed,
        configuration: _configuration,
        metadata: {
          'method': 'full_initialization',
          'state_type': finalState.runtimeType.toString(),
        },
      );
    } catch (error) {
      stopwatch.stop();
      rethrow;
    }
  }

  // ---------------------------------------------------------------------------
  // Initialization Steps
  // ---------------------------------------------------------------------------

  /// Initialize token storage system
  Future<void> _initializeTokenStorage() async {
    try {
      developer.log(
        'Initializing token storage system',
        name: 'AuthInitializationService',
      );

      // Validate storage integrity if supported - but don't clear data on failure
      try {
        final isIntegrityValid = await _tokenStorage.validateStorageIntegrity();

        if (!isIntegrityValid) {
          developer.log(
            'Storage integrity validation failed - continuing without clearing data',
            name: 'AuthInitializationService',
            level: 900,
          );
          // Don't clear all data - just log the warning and continue
          // This prevents automatic sign-out during initialization
        }
      } catch (error) {
        developer.log(
          'Storage integrity check failed: $error - continuing without validation',
          name: 'AuthInitializationService',
          error: error,
        );
        // Continue without integrity check - don't clear data
      }

      // Clean up expired tokens only (don't clear all data)
      await _tokenStorage.clearExpiredTokens();

      developer.log(
        'Token storage system initialized successfully',
        name: 'AuthInitializationService',
      );
    } catch (error) {
      developer.log(
        'Failed to initialize token storage: $error',
        name: 'AuthInitializationService',
        error: error,
      );
      throw Exception('Token storage initialization failed: $error');
    }
  }

  /// Check for existing authentication state
  Future<AuthState?> _checkExistingAuthState() async {
    try {
      developer.log(
        'Checking for existing authentication state',
        name: 'AuthInitializationService',
      );

      // Check for stored tokens
      final accessToken = await _tokenStorage.getToken();
      final refreshToken = await _tokenStorage.getRefreshToken();

      developer.log(
        'Token check: accessToken=${accessToken != null ? "found" : "null"}, refreshToken=${refreshToken != null ? "found" : "null"}',
        name: 'AuthInitializationService',
      );

      if (accessToken == null) {
        developer.log(
          'No stored access token found',
          name: 'AuthInitializationService',
        );
        return null;
      }

      // Check if token is expired
      final isExpired = await _tokenStorage.isTokenExpired();
      if (isExpired) {
        developer.log(
          'Stored access token is expired',
          name: 'AuthInitializationService',
        );

        // Try to refresh if refresh token is available
        if (refreshToken != null) {
          return await _attemptTokenRefresh(refreshToken);
        }

        return null;
      }

      // Try to restore user session from stored data
      final sessionData = await _tokenStorage.getSessionData();
      developer.log(
        'Session data check: isEmpty=${sessionData.isEmpty}, hasUser=${sessionData['user'] != null}',
        name: 'AuthInitializationService',
      );

      if (sessionData.isNotEmpty && sessionData['user'] != null) {
        try {
          final user = User.fromJson(
            sessionData['user'] as Map<String, dynamic>,
          );

          developer.log(
            'Restored user session for: ${user.email} with extended token validity',
            name: 'AuthInitializationService',
          );

          // Create authenticated state with extended session
          final authenticatedState = AuthState.authenticated(
            user: user,
            token: accessToken,
            refreshToken: refreshToken,
          );

          // Update session timestamp to extend validity
          await _tokenStorage.storeSessionData({
            'user': user.toJson(),
            'last_login': DateTime.now().toIso8601String(),
            'session_extended': true,
          });

          return authenticatedState;
        } catch (error) {
          developer.log(
            'Failed to restore user from session data: $error',
            name: 'AuthInitializationService',
            error: error,
          );
        }
      }

      return null;
    } catch (error) {
      developer.log(
        'Error checking existing auth state: $error',
        name: 'AuthInitializationService',
        error: error,
      );
      return null;
    }
  }

  /// Attempt to refresh expired token
  Future<AuthState?> _attemptTokenRefresh(String refreshToken) async {
    try {
      developer.log(
        'Attempting token refresh',
        name: 'AuthInitializationService',
      );

      final response = await _internalDio.post(
        '${BackendConfig.baseUrl}/api/v1/auth/refresh',
        data: {'refresh_token': refreshToken},
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        final newAccessToken = data['access_token'] as String;
        final newRefreshToken = data['refresh_token'] as String?;
        final expiresIn = data['expires_in'] as int?;

        // Store new tokens
        await _tokenStorage.storeToken(
          newAccessToken,
          expiryDate: expiresIn != null
              ? DateTime.now().add(Duration(seconds: expiresIn))
              : DateTime.now().add(const Duration(hours: 1)),
        );

        if (newRefreshToken != null) {
          await _tokenStorage.storeRefreshToken(
            newRefreshToken,
            expiryDate: DateTime.now().add(const Duration(days: 30)),
          );
        }

        // Try to restore user session
        final sessionData = await _tokenStorage.getSessionData();
        if (sessionData.isNotEmpty && sessionData['user'] != null) {
          final user = User.fromJson(
            sessionData['user'] as Map<String, dynamic>,
          );

          developer.log(
            'Token refresh successful for user: ${user.email}',
            name: 'AuthInitializationService',
          );

          return AuthState.authenticated(
            user: user,
            token: newAccessToken,
            refreshToken: newRefreshToken,
          );
        }
      }

      return null;
    } catch (error) {
      developer.log(
        'Token refresh failed: $error',
        name: 'AuthInitializationService',
        error: error,
      );
      return null;
    }
  }

  /// Validate stored tokens against backend
  Future<AuthState?> _validateStoredTokens(AuthStateAuthenticated state) async {
    try {
      developer.log(
        'Validating stored tokens against backend',
        name: 'AuthInitializationService',
      );

      // Create a temporary Dio instance to avoid circular dependency
      // with the auth provider during token validation
      final tempDio = Dio(BaseOptions(
        baseUrl: _configuration.apiBaseUrl,
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {
          'Authorization': 'Bearer ${state.token}',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ));

      final response = await tempDio.get('/api/v1/users/profile');

      if (response.statusCode == 200 && response.data != null) {
        developer.log(
          'Token validation successful',
          name: 'AuthInitializationService',
        );
        return state;
      } else {
        developer.log(
          'Token validation failed: Status ${response.statusCode}',
          name: 'AuthInitializationService',
        );
        return null;
      }
    } catch (error) {
      developer.log(
        'Token validation failed: $error',
        name: 'AuthInitializationService',
        error: error,
      );
      return null;
    }
  }

  /// Initialize authentication providers
  Future<void> _initializeAuthProviders() async {
    try {
      developer.log(
        'Initializing authentication providers',
        name: 'AuthInitializationService',
      );

      // Initialize Google Auth if enabled
      if (_configuration.googleAuthEnabled) {
        try {
          // Check if Google Auth is properly configured
          if (_googleAuthService.isConfigured) {
            developer.log(
              'Google Auth provider initialized',
              name: 'AuthInitializationService',
            );
          } else {
            developer.log(
              'Google Auth provider not configured - skipping',
              name: 'AuthInitializationService',
              level: 900,
            );
          }
        } catch (error) {
          developer.log(
            'Google Auth provider initialization failed: $error',
            name: 'AuthInitializationService',
            error: error,
          );
          // Don't fail entire initialization for Google Auth issues
        }
      }

      // Email auth is always available through the API
      if (_configuration.emailAuthEnabled) {
        developer.log(
          'Email Auth provider initialized',
          name: 'AuthInitializationService',
        );
      }

      developer.log(
        'Authentication providers initialized successfully',
        name: 'AuthInitializationService',
      );
    } catch (error) {
      developer.log(
        'Failed to initialize auth providers: $error',
        name: 'AuthInitializationService',
        error: error,
      );
      throw Exception('Auth providers initialization failed: $error');
    }
  }

  /// Set up monitoring and cleanup
  void _setupMonitoringAndCleanup() {
    developer.log(
      'Setting up monitoring and cleanup',
      name: 'AuthInitializationService',
    );

    // Set up periodic token cleanup
    Timer.periodic(const Duration(hours: 1), (timer) {
      _tokenStorage.clearExpiredTokens().catchError((error) {
        developer.log(
          'Periodic token cleanup failed: $error',
          name: 'AuthInitializationService',
          error: error,
        );
      });
    });

    developer.log(
      'Monitoring and cleanup configured',
      name: 'AuthInitializationService',
    );
  }

  // ---------------------------------------------------------------------------
  // Configuration Validation
  // ---------------------------------------------------------------------------

  /// Validate authentication configuration
  Future<ConfigurationValidationResult> validateConfiguration() async {
    try {
      developer.log(
        'Validating authentication configuration',
        name: 'AuthInitializationService',
      );

      final errors = <String>[];
      final warnings = <String>[];

      // Basic configuration validation
      errors.addAll(_configuration.validationErrors);

      // Network connectivity check
      try {
        final response = await _internalDio
            .get('${BackendConfig.baseUrl}/health')
            .timeout(const Duration(seconds: 3));

        if (response.statusCode != 200) {
          warnings.add(
            'Backend health check failed - offline mode may be used',
          );
        }
      } catch (error) {
        warnings.add('Cannot reach backend - offline mode may be used');
      }

      // Google Auth configuration check
      if (_configuration.googleAuthEnabled) {
        if (!_googleAuthService.isConfigured) {
          warnings.add('Google Auth is enabled but not properly configured');
        }
      }

      // Token storage check
      if (_configuration.useSecureStorage) {
        try {
          await _tokenStorage.getStorageMetadata();
        } catch (error) {
          errors.add('Secure storage is not available: $error');
        }
      }

      final isValid = errors.isEmpty;

      developer.log(
        'Configuration validation completed: ${isValid ? 'VALID' : 'INVALID'}',
        name: 'AuthInitializationService',
      );

      if (errors.isNotEmpty) {
        developer.log(
          'Configuration errors: ${errors.join(', ')}',
          name: 'AuthInitializationService',
          level: 1000,
        );
      }

      if (warnings.isNotEmpty) {
        developer.log(
          'Configuration warnings: ${warnings.join(', ')}',
          name: 'AuthInitializationService',
          level: 900,
        );
      }

      return ConfigurationValidationResult(
        isValid: isValid,
        errors: errors,
        warnings: warnings,
      );
    } catch (error, stackTrace) {
      developer.log(
        'Configuration validation failed: $error',
        name: 'AuthInitializationService',
        error: error,
        stackTrace: stackTrace,
      );

      return ConfigurationValidationResult(
        isValid: false,
        errors: ['Configuration validation failed: $error'],
        warnings: [],
      );
    }
  }

  // ---------------------------------------------------------------------------
  // Error Handling and Circuit Breaker
  // ---------------------------------------------------------------------------

  /// Handle initialization failure
  AuthInitializationResult _handleInitializationFailure(
    String error,
    AuthErrorType errorType, {
    required bool canRetry,
  }) {
    developer.log(
      'Handling initialization failure: $error',
      name: 'AuthInitializationService',
      level: 1000,
    );

    // Create fallback state based on error type
    AuthState fallbackState;

    switch (errorType) {
      case AuthErrorType.networkError:
        fallbackState = const AuthState.unauthenticated(
          reason: 'خطأ في الاتصال بالشبكة - يرجى التحقق من اتصالك بالإنترنت',
        );
        break;
      case AuthErrorType.serverError:
        fallbackState = const AuthState.unauthenticated(
          reason: 'خطأ في الخادم - يرجى المحاولة لاحقاً',
        );
        break;
      case AuthErrorType.serviceUnavailable:
        fallbackState = const AuthState.unauthenticated(
          reason: 'الخدمة غير متاحة حالياً - يرجى المحاولة لاحقاً',
        );
        break;
      default:
        fallbackState = const AuthState.unauthenticated(
          reason: 'فشل في تهيئة نظام المصادقة',
        );
    }

    return AuthInitializationResult.failure(
      error: error,
      errorType: errorType,
      canRetry: canRetry,
      fallbackState: fallbackState,
      details: {
        'failure_count': _failureCount,
        'circuit_open': _circuitOpen,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Check if circuit breaker is open
  bool _isCircuitOpen() {
    if (!_circuitOpen) return false;

    // Check if enough time has passed to try again
    if (_lastFailureTime != null) {
      final timeSinceLastFailure = DateTime.now().difference(_lastFailureTime!);
      if (timeSinceLastFailure.inSeconds >= 30) {
        _circuitOpen = false;
        developer.log(
          'Circuit breaker reset after ${timeSinceLastFailure.inSeconds}s',
          name: 'AuthInitializationService',
        );
        return false;
      }
    }

    return true;
  }

  /// Handle circuit breaker open state
  AuthInitializationResult _handleCircuitBreakerOpen() {
    developer.log(
      'Circuit breaker is open - skipping initialization',
      name: 'AuthInitializationService',
    );

    return AuthInitializationResult.failure(
      error: 'Circuit breaker is open - too many recent failures',
      errorType: AuthErrorType.serviceUnavailable,
      canRetry: false,
      fallbackState: const AuthState.unauthenticated(
        reason: 'النظام غير متاح مؤقتاً بسبب أخطاء متكررة',
      ),
      details: {
        'circuit_breaker': 'open',
        'failure_count': _failureCount,
        'last_failure': _lastFailureTime?.toIso8601String(),
      },
    );
  }

  /// Update circuit breaker on success
  void _updateCircuitBreakerOnSuccess() {
    if (_failureCount > 0 || _circuitOpen) {
      developer.log(
        'Resetting circuit breaker after successful initialization',
        name: 'AuthInitializationService',
      );
    }

    _failureCount = 0;
    _circuitOpen = false;
    _lastFailureTime = null;
  }

  /// Update circuit breaker on failure
  void _updateCircuitBreakerOnFailure() {
    _failureCount++;
    _lastFailureTime = DateTime.now();

    if (_failureCount >= _configuration.maxRetryAttempts) {
      _circuitOpen = true;
      developer.log(
        'Circuit breaker opened after $_failureCount failures',
        name: 'AuthInitializationService',
      );
    }
  }

  /// Categorize error for appropriate handling
  AuthErrorType _categorizeError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') ||
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return AuthErrorType.networkError;
    }

    if (errorString.contains('server') ||
        errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503')) {
      return AuthErrorType.serverError;
    }

    if (errorString.contains('unavailable') ||
        errorString.contains('service')) {
      return AuthErrorType.serviceUnavailable;
    }

    if (errorString.contains('token') ||
        errorString.contains('auth') ||
        errorString.contains('unauthorized')) {
      return AuthErrorType.sessionExpired;
    }

    return AuthErrorType.unknown;
  }

  /// Check if error can be retried
  bool _canRetryError(AuthErrorType errorType) {
    switch (errorType) {
      case AuthErrorType.networkError:
      case AuthErrorType.serverError:
      case AuthErrorType.serviceUnavailable:
        return true;
      case AuthErrorType.unknown:
        return _failureCount < _configuration.maxRetryAttempts;
      default:
        return false;
    }
  }

  // ---------------------------------------------------------------------------
  // Public Getters
  // ---------------------------------------------------------------------------

  /// Get current configuration
  AuthConfiguration get configuration => _configuration;

  /// Check if service has been initialized
  bool get hasInitialized => _hasInitialized;

  /// Check if initialization is in progress
  bool get isInitializing => _isInitializing;

  /// Get last initialization result
  AuthInitializationResult? get lastResult => _lastResult;

  /// Get circuit breaker status
  bool get isCircuitOpen => _circuitOpen;

  /// Get failure count
  int get failureCount => _failureCount;
}

// =============================================================================
// CONFIGURATION VALIDATION RESULT
// =============================================================================

/// Result of configuration validation
class ConfigurationValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ConfigurationValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  /// Check if there are any issues
  bool get hasIssues => errors.isNotEmpty || warnings.isNotEmpty;

  /// Get all issues as a single list
  List<String> get allIssues => [...errors, ...warnings];
}

// =============================================================================
// RIVERPOD PROVIDERS
// =============================================================================

/// Provider for AuthInitializationService
@riverpod
AuthInitializationService authInitializationService(Ref ref) {
  return AuthInitializationService(ref: ref);
}

/// Provider for auth configuration
@riverpod
AuthConfiguration authConfiguration(Ref ref) {
  // In production, this could be loaded from environment variables
  // or remote configuration
  return const AuthConfiguration(
    apiBaseUrl: 'https://backend-go-8klm.onrender.com',
    googleAuthEnabled: true,
    emailAuthEnabled: true,
    initializationTimeout: Duration(seconds: 60), // Increased from 30 to 60 seconds
    maxRetryAttempts: 3,
    enableOfflineMode: true,
    validateStoredTokens: true,
    useSecureStorage: true,
  );
}

/// Provider for initialization result
@riverpod
Future<AuthInitializationResult> authInitializationResult(Ref ref) async {
  final service = ref.read(authInitializationServiceProvider);
  return await service.initialize();
}
