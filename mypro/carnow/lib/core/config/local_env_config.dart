/// ============================================================================
/// LOCAL ENV CONFIG - Forever Plan Architecture
/// ============================================================================
///
/// Local environment configuration fallbacks
/// Used when .env file is not available
/// ============================================================================
library;

import 'package:flutter/foundation.dart';
import 'dart:io';

/// Local environment configuration with fallback values
class LocalEnvConfig {
  // Supabase Configuration
  static const String supabaseUrl = 'https://lpxtghyvxuenyyisrrro.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxweHRnaHl2eHVlbnl5aXNycnJvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI2MjIxNzgsImV4cCI6MjA2ODE5ODE3OH0.7GQrSfJwLQskYOcu9nusXF1amMnezpw3s96sk-zHQ64';

  // API Configuration - Use local backend for development
  static String get apiBaseUrl {
    // Use local backend for development
    return 'http://localhost:8081';
  }

  // Google OAuth Configuration
  static const String googleWebClientId = '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com';
  static const String googleAndroidClientId = '630980378491-vbd1bsp32o4g0664pmt1mhbj9raccnem.apps.googleusercontent.com';
  
  // Redirect URLs
  static const String webRedirectUrl = 'https://lpxtghyvxuenyyisrrro.supabase.co/auth/v1/callback';
  static const String googleMobileRedirectUrl = 'https://lpxtghyvxuenyyisrrro.supabase.co/auth/v1/callback';
  static const String googleMobileRedirectUrlSecondary = 'https://lpxtghyvxuenyyisrrro.supabase.co/auth/v1/callback';
  static const String webRedirectUrlFallback = 'https://lpxtghyvxuenyyisrrro.supabase.co/auth/v1/callback';

  // Performance Configuration
  static const bool enablePerformanceMonitoring = true;
  static const double performanceSampleRate = 0.1;

  // Debug Configuration
  static const bool enableDebugLogging = true;
  static const bool enableNetworkLogging = true;
  static const bool enableErrorReporting = true;

  // Feature Flags
  static const bool enableGoogleOAuth = true;
  static const bool enableEmailAuth = true;
  static const bool enableOfflineMode = true;
  static const bool enablePushNotifications = true;
}
